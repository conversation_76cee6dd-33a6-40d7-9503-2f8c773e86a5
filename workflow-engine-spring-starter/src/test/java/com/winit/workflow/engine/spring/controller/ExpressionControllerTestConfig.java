package com.winit.workflow.engine.spring.controller;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * 专门用于 ExpressionControllerTest 的测试配置类
 * 避免与其他 @SpringBootConfiguration 类冲突
 */
@SpringBootApplication
@ComponentScan(basePackages = "com.winit.workflow.engine.spring.controller")
public class ExpressionControllerTestConfig {
    // 空配置类，仅用于测试
}
