package com.winit.workflow.engine.spring.config;

import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

/**
 * Test configuration for Spring Boot tests
 */
@SpringBootConfiguration
@EnableAutoConfiguration
@ComponentScan(basePackages = {
    "com.winit.workflow.engine.spring.controller",
    "com.winit.workflow.engine.spring.config"
})
@Import(ExpressionConfiguration.class)
public class TestConfiguration {

    /**
     * 提供 TestRestTemplate bean 用于集成测试
     * 确保在 Java 1.8 环境下的兼容性
     */
    @Bean
    public TestRestTemplate testRestTemplate() {
        return new TestRestTemplate();
    }
}
