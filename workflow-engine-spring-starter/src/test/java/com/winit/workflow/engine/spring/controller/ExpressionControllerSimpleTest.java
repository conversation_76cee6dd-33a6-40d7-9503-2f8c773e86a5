package com.winit.workflow.engine.spring.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.winit.workflow.engine.core.expression.FunctionInfo;
import com.winit.workflow.engine.spring.config.TestConfiguration;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简化的集成测试，用于验证 ExpressionController REST API
 */
@SpringBootTest(
    classes = TestConfiguration.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
@TestPropertySource(properties = {
    "spring.main.allow-bean-definition-overriding=true",
    "logging.level.com.winit.workflow.engine=DEBUG"
})
public class ExpressionControllerSimpleTest {
    
    @LocalServerPort
    private int port;
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    private ObjectMapper objectMapper;
    private String baseUrl;
    
    @BeforeEach
    public void setUp() {
        objectMapper = new ObjectMapper();
        baseUrl = "http://localhost:" + port + "/api/v1/expression";
    }
    
    @Test
    public void testGetAllFunctions() throws Exception {
        System.out.println("\n=== 测试 GET /api/v1/expression/functions ===");
        
        // 调用 REST API
        ResponseEntity<String> response = restTemplate.getForEntity(
            baseUrl + "/functions", String.class);
        
        // 验证 HTTP 状态
        assertEquals(HttpStatus.OK, response.getStatusCode());
        
        // 解析 JSON 响应
        List<FunctionInfo> functions = objectMapper.readValue(
            response.getBody(), new TypeReference<List<FunctionInfo>>() { });
        
        // 验证函数数量
        assertNotNull(functions);
        assertTrue(functions.size() >= 10, "应该至少有 10 个函数"); // 至少应该有我们的所有函数
        
        // 打印所有函数进行验证
        System.out.println("检索到 " + functions.size() + " 个函数:");
        System.out.println(StringUtils.repeat("=", 80));
        
        int builtinCount = 0;
        int customCount = 0;
        
        for (FunctionInfo function : functions) {
            System.out.printf("函数: %-15s | 类型: %-8s | 优先级: %-3d | 参数: %s%n",
                function.getFunctionName(),
                function.getType(),
                function.getPriority(),
                function.getParameterCount() == -1 ? "可变" : String.valueOf(function.getParameterCount())
            );
            
            // 按类型计数
            if ("builtin".equals(function.getType())) {
                builtinCount++;
            } else if ("custom".equals(function.getType())) {
                customCount++;
            }
            
            // 验证函数包含所有必需字段
            assertNotNull(function.getFunctionName());
            assertNotNull(function.getDescription());
            assertNotNull(function.getType());
            assertNotNull(function.getFunctionSignature());
            assertNotNull(function.getExample());
            assertTrue(function.getPriority() > 0);
            assertTrue(function.getParameterCount() >= -1);
        }
        
        System.out.println(StringUtils.repeat("=", 80));
        System.out.printf("总结: %d 个内置函数, %d 个自定义函数%n", builtinCount, customCount);
        
        // 验证特定函数存在
        assertTrue(functions.stream().anyMatch(f -> "abs".equals(f.getFunctionName())), "应包含 abs 函数");
        assertTrue(functions.stream().anyMatch(f -> "max".equals(f.getFunctionName())), "应包含 max 函数");
        assertTrue(functions.stream().anyMatch(f -> "min".equals(f.getFunctionName())), "应包含 min 函数");
        assertTrue(functions.stream().anyMatch(f -> "concat".equals(f.getFunctionName())), "应包含 concat 函数");
        
        System.out.println("✅ 所有函数验证通过!");
    }
    
    @Test
    public void testGetFunctionByName() throws Exception {
        System.out.println("\n=== 测试 GET /api/v1/expression/functions/{name} ===");
        
        // 测试存在的函数
        ResponseEntity<String> response = restTemplate.getForEntity(
            baseUrl + "/functions/abs", String.class);

        assertEquals(HttpStatus.OK, response.getStatusCode());

        FunctionInfo function = objectMapper.readValue(response.getBody(), FunctionInfo.class);
        assertNotNull(function);
        assertEquals("abs", function.getFunctionName());
        
        System.out.println("检索到函数: " + function.getFunctionName());
        System.out.println("描述: " + function.getDescription());
        
        // 测试不存在的函数
        ResponseEntity<String> notFoundResponse = restTemplate.getForEntity(
            baseUrl + "/functions/nonexistent", String.class);
        
        assertEquals(HttpStatus.NOT_FOUND, notFoundResponse.getStatusCode());
        
        System.out.println("✅ 函数查询测试通过!");
    }
    
    @Test
    public void testHealthEndpoint() throws Exception {
        System.out.println("\n=== 测试 GET /api/v1/expression/health ===");

        ResponseEntity<String> response = restTemplate.getForEntity(
            baseUrl + "/health", String.class);

        assertNotNull(response, "响应不应为 null");
        assertEquals(HttpStatus.OK, response.getStatusCode());

        String healthMessage = response.getBody();
        assertNotNull(healthMessage, "响应体不应为 null");
        assertTrue(healthMessage.contains("healthy"), "响应应包含 'healthy'");
        assertTrue(healthMessage.contains("functions available"), "响应应包含 'functions available'");

        System.out.println("健康检查响应: " + healthMessage);
        System.out.println("✅ 健康检查测试通过!");
    }
}
