package com.winit.workflow.engine.core.provider.smartengine;

import com.winit.workflow.engine.core.executor.service.ServiceExecutor;

import java.util.Map;

/**
 * 测试用的 ServiceExecutor 实现类
 * 用于替代 Mockito Mock 对象，解决 Java 23 兼容性问题
 */
public class TestServiceExecutor extends ServiceExecutor {
    
    private Object mockResult;
    private RuntimeException mockException;
    private boolean shouldThrowException = false;
    
    public TestServiceExecutor() {
        super();
    }
    
    /**
     * 设置模拟的执行结果
     */
    public void setMockResult(Object result) {
        this.mockResult = result;
        this.shouldThrowException = false;
    }
    
    /**
     * 设置模拟的异常
     */
    public void setMockException(RuntimeException exception) {
        this.mockException = exception;
        this.shouldThrowException = true;
    }
    
    @Override
    public Object execute(Map<String, Object> input) {
        if (shouldThrowException && mockException != null) {
            throw mockException;
        }
        return mockResult;
    }
    
    /**
     * 重置模拟状态
     */
    public void reset() {
        this.mockResult = null;
        this.mockException = null;
        this.shouldThrowException = false;
    }
}
