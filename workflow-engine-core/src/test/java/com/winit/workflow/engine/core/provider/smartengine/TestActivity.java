package com.winit.workflow.engine.core.provider.smartengine;

import com.alibaba.smart.framework.engine.model.assembly.Activity;
import com.alibaba.smart.framework.engine.model.assembly.ExtensionElements;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试用的 Activity 实现类
 * 用于替代 Mockito Mock 对象，解决 Java 23 兼容性问题
 */
public class TestActivity implements Activity {
    
    private String id;
    private String name;
    private Map<String, String> properties = new HashMap<>();
    private ExtensionElements extensionElements;
    
    public TestActivity() {
        this("testActivity");
    }
    
    public TestActivity(String id) {
        this.id = id;
        this.name = "Test Activity";
    }
    
    @Override
    public String getId() {
        return id;
    }
    
    @Override
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ExtensionElements getExtensionElements() {
        return extensionElements;
    }

    public void setExtensionElements(ExtensionElements extensionElements) {
        this.extensionElements = extensionElements;
    }

    @Override
    public Map<String, String> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, String> properties) {
        this.properties = properties;
    }

    public String getProperty(String key) {
        return properties.get(key);
    }

    public void setProperty(String key, String value) {
        properties.put(key, value);
    }

    @Override
    public boolean isStartActivity() {
        return false;
    }
    
    // 其他可能需要的方法的默认实现
    @Override
    public String toString() {
        return "TestActivity{id='" + id + "', name='" + name + "'}";
    }
}
