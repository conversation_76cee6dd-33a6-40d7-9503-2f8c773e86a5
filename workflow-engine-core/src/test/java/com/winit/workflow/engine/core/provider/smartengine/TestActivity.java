package com.winit.workflow.engine.core.provider.smartengine;

import com.alibaba.smart.framework.engine.model.assembly.Activity;
import com.alibaba.smart.framework.engine.model.assembly.ExtensionElements;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试用的 Activity 实现类
 * 用于替代 Mockito Mock 对象，解决 Java 23 兼容性问题
 */
public class TestActivity implements Activity {
    
    private String id;
    private String name;
    private Map<String, Object> properties = new HashMap<>();
    private ExtensionElements extensionElements;
    
    public TestActivity() {
        this("testActivity");
    }
    
    public TestActivity(String id) {
        this.id = id;
        this.name = "Test Activity";
    }
    
    @Override
    public String getId() {
        return id;
    }
    
    @Override
    public void setId(String id) {
        this.id = id;
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public void setName(String name) {
        this.name = name;
    }
    
    @Override
    public ExtensionElements getExtensionElements() {
        return extensionElements;
    }
    
    @Override
    public void setExtensionElements(ExtensionElements extensionElements) {
        this.extensionElements = extensionElements;
    }
    
    @Override
    public Map<String, Object> getProperties() {
        return properties;
    }
    
    @Override
    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }
    
    @Override
    public Object getProperty(String key) {
        return properties.get(key);
    }
    
    @Override
    public void setProperty(String key, Object value) {
        properties.put(key, value);
    }
    
    // 其他可能需要的方法的默认实现
    @Override
    public String toString() {
        return "TestActivity{id='" + id + "', name='" + name + "'}";
    }
}
