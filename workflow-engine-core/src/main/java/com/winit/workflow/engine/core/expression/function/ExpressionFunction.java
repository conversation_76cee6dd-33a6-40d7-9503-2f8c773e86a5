package com.winit.workflow.engine.core.expression.function;

/**
 * 表达式函数接口，定义自定义表达式函数的标准规范
 * 
 * 所有自定义表达式函数都应该实现此接口，以便被自动发现和注册
 */
public interface ExpressionFunction {
    
    /**
     * 获取函数名称
     * 
     * @return 函数名称，用于在表达式中调用
     */
    String getFunctionName();
    
    /**
     * 获取函数描述
     * 
     * @return 函数的描述信息
     */
    default String getDescription() {
        return "Custom expression function: " + getFunctionName();
    }
    
    /**
     * 获取函数的参数数量
     *
     * @return 参数数量，-1表示可变参数
     */
    default int getParameterCount() {
        return -1; // 默认支持可变参数
    }

    /**
     * 获取参数信息的字符串表示
     * 提供比 getParameterCount() 更清晰的参数描述
     *
     * @return 参数信息，如 "2", "variable", "2-3", "1+"
     */
    default String getParameterInfo() {
        int paramCount = getParameterCount();
        if (paramCount == -1) {
            return "variable";
        } else if (paramCount == 0) {
            return "0";
        } else {
            return String.valueOf(paramCount);
        }
    }
    
    /**
     * 执行函数
     * 
     * @param args 函数参数
     * @return 函数执行结果
     */
    Object execute(Object... args);
    
    /**
     * 验证参数
     * 
     * @param args 函数参数
     * @return 如果参数有效返回true，否则返回false
     */
    default boolean validateParameters(Object... args) {
        int paramCount = getParameterCount();
        if (paramCount >= 0) {
            return args != null && args.length == paramCount;
        }
        return true; // 可变参数默认总是有效
    }
    
    /**
     * 获取函数的优先级
     * 数值越小优先级越高，用于处理同名函数的覆盖
     *
     * @return 优先级，默认为100
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 获取函数的使用示例
     *
     * @return 使用示例，展示如何在表达式中调用该函数
     */
    default String getExample() {
        return "${" + getFunctionName() + "(...)}";
    }

    /**
     * 获取函数签名
     * 描述函数的调用格式和参数说明
     *
     * @return 函数签名字符串
     */
    default String getFunctionSignature() {
        String name = getFunctionName();
        int paramCount = getParameterCount();

        if (paramCount == 0) {
            return name + "(): no parameters";
        } else if (paramCount == 1) {
            return name + "(arg): 1 parameter";
        } else if (paramCount > 1) {
            StringBuilder signature = new StringBuilder();
            signature.append(name).append("(");
            for (int i = 1; i <= paramCount; i++) {
                if (i > 1) {
                    signature.append(", ");
                }
                signature.append("arg").append(i);
            }
            signature.append("): ").append(paramCount).append(" parameters");
            return signature.toString();
        } else {
            return name + "(arg1, arg2, ...): variable arguments";
        }
    }
}
