<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.winit</groupId>
        <artifactId>workflow-engine</artifactId>
        <version>${workflow-engine.version}</version>
    </parent>

    <artifactId>workflow-engine-provider</artifactId>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.smart.framework</groupId>
            <artifactId>smart-engine-extension-storage-custom</artifactId>
            <version>${smart-engine.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.smart.framework</groupId>
            <artifactId>smart-engine-extension-retry-custom</artifactId>
            <version>${smart-engine.version}</version>
        </dependency>

        <dependency>
            <groupId>com.winit</groupId>
            <artifactId>workflow-engine-core</artifactId>
            <version>${workflow-engine.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.mvel</groupId>
            <artifactId>mvel2</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.1</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>