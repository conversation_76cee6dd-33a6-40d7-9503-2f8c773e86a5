# Built-in functions (priority 10)
com.winit.workflow.engine.core.expression.function.builtin.ConcatFunction
com.winit.workflow.engine.core.expression.function.builtin.LowercaseFunction
com.winit.workflow.engine.core.expression.function.builtin.NowFunction
com.winit.workflow.engine.core.expression.function.builtin.UppercaseFunction

# SDK functions (priority 100)
com.winit.workflow.engine.sdk.expression.function.CalculateTaxFunction
com.winit.workflow.engine.sdk.expression.function.GenerateOrderNumberFunction

# Custom functions (priority 200)
com.winit.workflow.engine.core.expression.function.custom.AbsFunction
com.winit.workflow.engine.core.expression.function.custom.CompareDateFunction
com.winit.workflow.engine.core.expression.function.custom.ConvertLengthFunction
com.winit.workflow.engine.core.expression.function.custom.ToIntFunction
com.winit.workflow.engine.core.expression.function.custom.ToStringFunction
com.winit.workflow.engine.core.expression.function.custom.MaxFunction
com.winit.workflow.engine.core.expression.function.custom.MinFunction
com.winit.workflow.engine.core.expression.function.custom.RoundFunction
com.winit.workflow.engine.core.expression.function.custom.SubstringFunction
com.winit.workflow.engine.core.expression.function.custom.ContainsFunction
